<div   class="breadcrumb-container position-lg-fixed top-2"
[ngClass]="getBreadcrumbSize()">
  <ng-container *ngFor="let item of breadcrumbs; let last = last">
    <!-- Normal breadcrumb item -->
    <ng-container *ngIf="!item.disabled && !item.isOverflowed">
      <a class="breadcrumb-item" data-testid="breadcrumb-item" (click)="onBreadcrumbClick(item)">{{ item.label |translate}}</a>
    </ng-container>

    <!-- Disabled breadcrumb item -->
    <ng-container *ngIf="item.disabled">
      <a class="breadcrumb-item disabled" data-testid="breadcrumb-item">{{ item.label |translate}}</a>
    </ng-container>

    <!-- Divider between breadcrumb items -->
    <ng-container *ngIf="!last">
      <span class="breadcrumb-divider">{{ divider }}</span>
    </ng-container>
  </ng-container>
</div>
