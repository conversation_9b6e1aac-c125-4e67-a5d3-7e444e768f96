<!-- Breadcrumb Navigation -->
<div class="breadcrumb-container">
  <app-breadcrumb
    [breadcrumbs]="breadcrumbItems"
    [size]="breadcrumbSizeEnum.Small"
    (onClickEvent)="onBreadcrumbClicked($event)">
  </app-breadcrumb>
</div>
<div class="d-flex justify-content-between">
  <div
    class="header-container w-100 d-flex align-items-center justify-content-between">
    <div class="d-flex">
      <img [src]="'assets/icons/square-arrow-left.png'" alt="edit"
        style="width:
    20.5px;height: 20.5px; margin-left: 10px; " />
      <div class="title-container">
        <p class="title">
          {{'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS' | translate }}
        </p>
        <p class="sub-title" *ngIf="resolution!= null && resolution.parentResolutionId != null">
          {{'INVESTMENT_FUNDS.RESOLUTIONS.CODE_RELATED_DECISION' | translate }}:
          <span>
            {{resolution?.parentResolutionCode}}
          </span>

        </p>
      </div>
    </div>
    <!-- Dialog Actions -->
    <div class="dialog-actions d-flex justify-content-end mt-4">

      <app-custom-button [btnName]="'COMMON.CANCEL' | translate"
        (click)="cancel()"
        [buttonType]="buttonEnum.Secondary" [iconName]="IconEnum.cancel">
      </app-custom-button>

      <app-custom-button
        *ngIf="resolution?.status === 1 || resolution?.status === 2"
        [btnName]="'COMMON.EDIT' | translate"
        (click)="editResolution(resolution)" class="mx-2"
        [buttonType]="buttonEnum.OutLine" [iconName]="IconEnum.edit">
      </app-custom-button>

      <app-custom-button
        *ngIf="resolution?.status === 2"
        [btnName]="'COMMON.Complete' | translate"
        (click)="completeResolution(resolution)" class="mx-2"
        [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify">
      </app-custom-button>

      <!-- Confirm/Reject buttons for Fund Manager -->
      <app-custom-button
        *ngIf="shouldShowConfirmRejectButtons()"
        [btnName]="'RESOLUTIONS.CONFIRM' | translate"
        (click)="onConfirmResolution()" class="mx-2"
        [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify">
      </app-custom-button>

      <app-custom-button
        *ngIf="shouldShowConfirmRejectButtons()"
        [btnName]="'RESOLUTIONS.REJECT' | translate"
        (click)="onRejectResolution()" class="mx-2"
        [buttonType]="buttonEnum.Danger" [iconName]="IconEnum.cancel">
      </app-custom-button>

      <!-- Send to Vote button for Legal Council/Board Secretary -->
      <app-custom-button
        *ngIf="shouldShowSendToVoteButton()"
        [btnName]="'RESOLUTIONS.SEND_TO_VOTE' | translate"
        (click)="onSendToVote()" class="mx-2"
        [buttonType]="buttonEnum.Primary" [iconName]="IconEnum.verify">
      </app-custom-button>
    </div>
  </div>

</div>
<!-- Main Content -->

<div class="row">
  <div class=" col-8">
    <div class=" resolution-details-container">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'FUND_DETAILS.BASIC_INFO' | translate }}
        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpand()">
            <img [src]="isExpanded ?'assets/images/accrdion_up.png'
              :'assets/images/accrdion_down.png'" alt="edit" style="width:
              14px;height: 8px;" />
          </button>

        </div>
      </div>
      <hr *ngIf="isExpanded" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" [class.expanded]="isExpanded">
        <div class="row" style="margin-bottom: 28px;">
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_CODE' | translate }}</p>
            <p class="info-value">{{ resolution?.code }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_OLD_CODE' | translate
              }}</p>
            <p class="info-value">{{ resolution?.oldResolutionCode }}</p>
          </div>

          <!-- Note: Parent Resolution and Old Resolution Code properties are not available in current API model -->
          <!-- Resolution Date -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DATE' | translate }}</p>
            <p class="info-value">{{ formatDate(resolution?.resolutionDate)
              }}</p>
          </div>

          <!-- Status -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.STATUS' |
              translate }}</p>
            <!-- <p class="info-value">{{ resolution?.resolutionStatus?.nameAr ||
              resolution?.resolutionStatus?.nameEn || getStatusDisplay() }}</p> -->

            <span class="status" [ngClass]="getStatusClass(resolution?.statusId)">
              {{resolution?.resolutionStatus?.localizedName | translate }}
            </span>
          </div>

          <!-- Resolution Type -->
          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.TYPE_DECISION' | translate }}</p>
            <p class="info-value">{{ resolution?.resolutionType?.nameAr ||
              resolution?.resolutionType?.nameEn || 'N/A' }}</p>
          </div>

        </div>
        <div class="row" style="margin-bottom: 28px;">

          <div class="col-md-3 info-item"
            *ngIf="resolution?.resolutionType?.isOther">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.TYPE_DECISION_ADDED' | translate
              }}</p>
            <p class="info-value">{{ resolution?.newType || 'N/A' }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.MEMBER_VOTING_DECISION' | translate
              }}</p>
            <p class="info-value">{{ resolution?.votingTypeDisplay }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label">{{
              'INVESTMENT_FUNDS.RESOLUTIONS.MEMBER_VOTING_RESULT' | translate
              }}</p>
            <p class="info-value">{{ resolution?.memberVotingResultDisplay
              }}</p>
          </div>

          <div class="col-md-3 info-item">
            <p class="info-label"> {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS'
              | translate }} </p>
            <p class="info-value">{{ resolution?.attachment?.fileName }}</p>
          </div>

        </div>

        <!-- Description -->
        <div class="col-md-3 info-item full-width"
          *ngIf="resolution?.description">
          <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.DESCRIPTION' |
            translate }}</p>
          <!-- <p class="info-value">{{ resolution?.description }}</p> -->
         <p class="info-value description-text" [title]="resolution.description"> {{ resolution.description }}</p>

        </div>

        <!-- Fund Name -->
        <!-- <div class="col-md-3 info-item">
        <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.FUND_NAME' | translate }}</p>
        <p class="info-value">{{ resolution?.fundName || 'N/A' }}</p>
      </div> -->
        <!-- Last Updated -->
        <!-- <div class="col-md-3 info-item" *ngIf="resolution?.lastUpdated">
        <p class="info-label">{{ 'INVESTMENT_FUNDS.RESOLUTIONS.LAST_UPDATED' | translate }}</p>
        <p class="info-value">{{ formatDate(resolution?.lastUpdated) }}</p>
      </div> -->
      </div>
    </div>

    <div class=" resolution-details-container mt-3">
      <div class="resolution-details-header" >
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS' | translate }}
          <span> {{resolution?.resolutionItems?.length }}{{
            'INVESTMENT_FUNDS.RESOLUTIONS.ITEMS_WITHOUT' | translate
            }}</span>

        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpandItems()">
            <img [src]="isExpandedItem ?'assets/images/accrdion_up.png'
              :'assets/images/accrdion_down.png'" alt="edit" style="width:
              14px;height: 8px;" />
          </button>

        </div>
      </div>
      <hr *ngIf="isExpandedItem" style="margin-bottom: 16PX;">
      <div class="resolution-details-content" style="max-height: 400px;
    overflow-y: scroll;" [class.expanded]="isExpandedItem">
        <div class="row" style="margin-bottom: 28px;"
          *ngFor="let item of resolution?.resolutionItems">
          <!-- *ngFor="let item of resolution?.resolutionItems" -->
          <div class="item-container col-12">
            <div class="top-section d-flex justify-content-between w-100
              align-items-center">
              <p class="title">
                {{item.title}} </p>
              <button class="conflict-btn"
                *ngIf="item.conflictMembersCount > 0 "
                (click)="onViewConflictMembers(item.conflictMembers)">
                {{'INVESTMENT_FUNDS.RESOLUTIONS.HAVE_CONFLICT' | translate }}

                ({{item?.conflictMembersCount }} أعضاء)
              </button>
            </div>
            <p class="sub-title">
              {{item?.description}}

            </p>
          </div>

        </div>
      </div>
    </div>

    <div class=" resolution-details-container mt-3">
      <div class="resolution-details-header">
        <p class="section-title navy-color">
          {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ACTIONS' | translate }}

        </p>
        <div class="header-actions">
          <button class="expand-button" (click)="toggleExpandActions()">
            <img [src]="isExpandedAction ?'assets/images/accrdion_up.png'
                :'assets/images/accrdion_down.png'" alt="edit" style="width:
                14px;height: 8px;" />
          </button>

        </div>

      </div>
      <hr *ngIf="isExpandedAction" style="margin-bottom: 16PX;">
      <div class="resolution-details-content"
        [class.expanded]="isExpandedAction">
        <app-timeline [steps]="resolutionStatus"></app-timeline>
      </div>
    </div>
  </div>

  <div class="attachment-section col-4">
    <p class="title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.FILES' | translate }}
    </p>
    <hr>
    <p class="sub-title">
      {{ 'INVESTMENT_FUNDS.RESOLUTIONS.DECISION_FILE' | translate }}
    </p>
    <div class="mb-3">
      <app-attachment-card
        [attachment]="resolution?.attachment"></app-attachment-card>

    </div>

    <hr>
    <div>
      <p class="sub-title">
        {{ 'INVESTMENT_FUNDS.RESOLUTIONS.ATTACHMENTS' | translate }}
        <span
          class="attachment-number">{{resolution?.otherAttachments?.length}}</span>
      </p>
      <app-attachment-card
        [attachment]="resolution?.otherAttachments"></app-attachment-card>
    </div>
  </div>
</div>
