.documents-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .page-header {
    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #1a365d;
      margin: 0;
    }

    .page-description {
      color: #718096;
      margin: 8px 0 0 0;
      font-size: 14px;
    }
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }

  .documents-content {
    .documents-tabs {
      ::ng-deep {
        .mat-mdc-tab-group {
          .mat-mdc-tab-header {
            border-bottom: 1px solid #e2e8f0;
            
            .mat-mdc-tab-label {
              font-weight: 500;
              color: #4a5568;
              
              &.mdc-tab--active {
                color: #2b6cb0;
              }
            }
          }

          .mat-mdc-tab-body-wrapper {
            padding-top: 24px;
          }
        }
      }
    }
  }
}

// RTL Support
[dir="rtl"] {
  .documents-container {
    .page-header {
      .d-flex {
        flex-direction: row-reverse;
      }
    }
  }
}
