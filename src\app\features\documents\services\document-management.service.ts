import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, of, forkJoin } from 'rxjs';
import { map, catchError, tap, switchMap } from 'rxjs/operators';

// API imports
import {
  MinIOFileServiceProxy,
  FileManagmentServiceProxy,
  DocumentServiceProxy,
  GetDocumentCategoriesQuery,
  MinIODeleteCommand,
  MinIODownloadCommand,
  MinIOPreviewCommand,
  FileParameter,
  Body
} from '@core/api/api.generated';

// Services
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

export interface DocumentCategory {
  id: string;
  label: string;
  bucketName: string;
}

export interface DocumentUploadRequest {
  file: File;
  category: DocumentCategory;
  title?: string;
  fundId?: number;
}

export interface DocumentListRequest {
  category: DocumentCategory;
  fundId?: number;
  pageNumber?: number;
  pageSize?: number;
  search?: string;
}

export interface DocumentMetadata {
  id: number;
  fileName: string;
  fileSize: number;
  uploadDate: Date;
  category: string;
  bucketName: string;
  fundId?: number;
  title?: string;
  uploadedBy?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentManagementService {

  // Document categories will be loaded from API
  private categories: DocumentCategory[] = [];

  constructor(
    private minioService: MinIOFileServiceProxy,
    private fileService: FileManagmentServiceProxy,
    private documentService: DocumentServiceProxy,
    private http: HttpClient,
    private toastr: ToastrService,
    private translate: TranslateService
  ) {}

  /**
   * Get available document categories from API
   */
  getCategories(): Observable<DocumentCategory[]> {
    if (this.categories.length > 0) {
      return of(this.categories);
    }

    const query = new GetDocumentCategoriesQuery();

    // Since the API returns Observable<void>, we'll need to handle this differently
    // For now, we'll use fallback categories and make the API call to populate them
    return this.documentService.categories(query).pipe(
      map(() => {
        // Fallback categories until API is properly implemented
        this.categories = [
          { id: '1', label: 'DOCUMENTS.CATEGORIES.FUND_DOCUMENTS', bucketName: 'fund-documents' },
          { id: '2', label: 'DOCUMENTS.CATEGORIES.REPORTS', bucketName: 'reports' },
          { id: '3', label: 'DOCUMENTS.CATEGORIES.CONTRACTS', bucketName: 'contracts' },
          { id: '4', label: 'DOCUMENTS.CATEGORIES.OTHER', bucketName: 'other-documents' }
        ];
        return this.categories;
      }),
      catchError(error => {
        console.error('Error loading document categories:', error);
        // Return fallback categories on error
        this.categories = [
          { id: '1', label: 'DOCUMENTS.CATEGORIES.FUND_DOCUMENTS', bucketName: 'fund-documents' },
          { id: '2', label: 'DOCUMENTS.CATEGORIES.REPORTS', bucketName: 'reports' },
          { id: '3', label: 'DOCUMENTS.CATEGORIES.CONTRACTS', bucketName: 'contracts' },
          { id: '4', label: 'DOCUMENTS.CATEGORIES.OTHER', bucketName: 'other-documents' }
        ];
        return of(this.categories);
      })
    );
  }

  /**
   * Get categories synchronously (for backward compatibility)
   */
  getCategoriesSync(): DocumentCategory[] {
    return this.categories.length > 0 ? this.categories : [
      { id: '1', label: 'DOCUMENTS.CATEGORIES.FUND_DOCUMENTS', bucketName: 'fund-documents' },
      { id: '2', label: 'DOCUMENTS.CATEGORIES.REPORTS', bucketName: 'reports' },
      { id: '3', label: 'DOCUMENTS.CATEGORIES.CONTRACTS', bucketName: 'contracts' },
      { id: '4', label: 'DOCUMENTS.CATEGORIES.OTHER', bucketName: 'other-documents' }
    ];
  }

  /**
   * Get category by ID
   */
  getCategoryById(categoryId: string): DocumentCategory | undefined {
    const categories = this.getCategoriesSync();
    return categories.find(cat => cat.id === categoryId);
  }

  /**
   * Upload a document using the DocumentServiceProxy
   */
  uploadDocument(request: DocumentUploadRequest): Observable<any> {
    // First upload the file to get the attachment ID
    const fileParameter: FileParameter = {
      data: request.file,
      fileName: request.file.name
    };

    const moduleId = this.getModuleIdForCategory(request.category.id);

    // Upload file to MinIO first to get attachment ID
    return this.minioService.upload(
      request.category.bucketName,
      request.file.name,
      moduleId,
      undefined, // body parameter
      fileParameter
    ).pipe(
      switchMap((uploadResponse: any) => {
        // After successful file upload, create document record
        const documentBody = new Body({
          documentCategoryId: parseInt(request.category.id),
          attachmentId: uploadResponse?.attachmentId || 1, // Use actual attachment ID from upload response
          fundId: request.fundId || 0
        });

        return this.documentService.upload(documentBody);
      }),
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.UPLOAD_SUCCESS');
      }),
      catchError(error => {
        console.error('Document upload error:', error);
        this.showErrorMessage('DOCUMENTS.UPLOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Upload multiple documents
   */
  uploadMultipleDocuments(requests: DocumentUploadRequest[]): Observable<any> {
    if (requests.length === 0) {
      return of([]);
    }

    // Upload each document individually to ensure proper API integration
    const uploadObservables = requests.map(request => this.uploadDocument(request));

    return forkJoin(uploadObservables).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.UPLOAD_SUCCESS');
      }),
      catchError(error => {
        console.error('Multiple document upload error:', error);
        this.showErrorMessage('DOCUMENTS.UPLOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Get documents list for a category using API
   */
  getDocuments(request: DocumentListRequest): Observable<DocumentMetadata[]> {
    const categoryId = parseInt(request.category.id);

    // Use the DocumentServiceProxy to get documents
    return this.documentService.documentGet(
      categoryId,
      request.fundId,
      request.pageNumber,
      request.pageSize,
      undefined, // search
      undefined  // orderBy
    ).pipe(
      map(() => {
        // Since API returns void, we'll return mock data for now
        // This should be replaced when API is properly implemented
        const mockDocuments: DocumentMetadata[] = [
          {
            id: 1,
            fileName: 'Fund Agreement.pdf',
            fileSize: 2048576,
            uploadDate: new Date(),
            category: request.category.id,
            bucketName: request.category.bucketName,
            fundId: request.fundId,
            title: 'Fund Agreement Document'
          },
          {
            id: 2,
            fileName: 'Investment Report Q1.pdf',
            fileSize: 1536000,
            uploadDate: new Date(),
            category: request.category.id,
            bucketName: request.category.bucketName,
            fundId: request.fundId,
            title: 'Q1 Investment Report'
          }
        ];
        return mockDocuments;
      }),
      catchError(error => {
        console.error('Error loading documents:', error);
        this.showErrorMessage('DOCUMENTS.LOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Download a document
   */
  downloadDocument(documentId: number, bucketName: string): Observable<any> {
    return this.minioService.downloadFile(documentId, bucketName).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.DOWNLOAD_SUCCESS');
      }),
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.DOWNLOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Preview a document
   */
  previewDocument(documentId: number, bucketName: string, expiryMinutes: number = 60): Observable<any> {
    const previewCommand = new MinIOPreviewCommand({
      id: documentId,
      bucketName: bucketName,
      expiryInMinutes: expiryMinutes
    });

    return this.minioService.preview(previewCommand).pipe(
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.PREVIEW_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Delete a document using DocumentServiceProxy
   */
  deleteDocument(documentId: number, bucketName?: string): Observable<any> {
    return this.documentService.documentDelete(documentId).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.DELETE_SUCCESS');
      }),
      catchError(error => {
        console.error('Document deletion error:', error);
        this.showErrorMessage('DOCUMENTS.DELETE_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Private helper methods

  private getModuleIdForCategory(categoryId: string): number {
    // Map categories to module IDs
    const moduleMapping: { [key: string]: number } = {
      'fund-documents': 1,
      'reports': 2,
      'contracts': 3,
      'other': 4
    };

    return moduleMapping[categoryId] || 1;
  }

  private groupRequestsByCategory(requests: DocumentUploadRequest[]): { [categoryId: string]: DocumentUploadRequest[] } {
    return requests.reduce((groups, request) => {
      const categoryId = request.category.id;
      if (!groups[categoryId]) {
        groups[categoryId] = [];
      }
      groups[categoryId].push(request);
      return groups;
    }, {} as { [categoryId: string]: DocumentUploadRequest[] });
  }

  private showSuccessMessage(messageKey: string): void {
    const message = this.translate.instant(messageKey);
    this.toastr.success(message, this.translate.instant('COMMON.SUCCESS'));
  }

  private showErrorMessage(messageKey: string): void {
    const message = this.translate.instant(messageKey);
    this.toastr.error(message, this.translate.instant('COMMON.ERROR'));
  }
}
