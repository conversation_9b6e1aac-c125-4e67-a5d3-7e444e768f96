import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';

// API imports
import {
  MinIOFileServiceProxy,
  FileManagmentServiceProxy,
  MinIODeleteCommand,
  MinIODownloadCommand,
  MinIOPreviewCommand,
  FileParameter
} from '@core/api/api.generated';

// Services
import { ToastrService } from 'ngx-toastr';
import { TranslateService } from '@ngx-translate/core';

export interface DocumentCategory {
  id: string;
  label: string;
  bucketName: string;
}

export interface DocumentUploadRequest {
  file: File;
  category: DocumentCategory;
  title?: string;
  fundId?: number;
}

export interface DocumentListRequest {
  category: DocumentCategory;
  fundId?: number;
  pageNumber?: number;
  pageSize?: number;
  search?: string;
}

export interface DocumentMetadata {
  id: number;
  fileName: string;
  fileSize: number;
  uploadDate: Date;
  category: string;
  bucketName: string;
  fundId?: number;
  title?: string;
  uploadedBy?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DocumentManagementService {
  
  // Document categories configuration
  private readonly categories: DocumentCategory[] = [
    { id: 'fund-documents', label: 'DOCUMENTS.CATEGORIES.FUND_DOCUMENTS', bucketName: 'fund-documents' },
    { id: 'reports', label: 'DOCUMENTS.CATEGORIES.REPORTS', bucketName: 'reports' },
    { id: 'contracts', label: 'DOCUMENTS.CATEGORIES.CONTRACTS', bucketName: 'contracts' },
    { id: 'other', label: 'DOCUMENTS.CATEGORIES.OTHER', bucketName: 'other-documents' }
  ];

  constructor(
    private minioService: MinIOFileServiceProxy,
    private fileService: FileManagmentServiceProxy,
    private http: HttpClient,
    private toastr: ToastrService,
    private translate: TranslateService
  ) {}

  /**
   * Get available document categories
   */
  getCategories(): DocumentCategory[] {
    return this.categories;
  }

  /**
   * Get category by ID
   */
  getCategoryById(categoryId: string): DocumentCategory | undefined {
    return this.categories.find(cat => cat.id === categoryId);
  }

  /**
   * Upload a document to MinIO storage
   */
  uploadDocument(request: DocumentUploadRequest): Observable<any> {
    const fileParameter: FileParameter = {
      data: request.file,
      fileName: request.file.name
    };

    const moduleId = this.getModuleIdForCategory(request.category.id);

    return this.minioService.upload(
      request.category.bucketName,
      request.file.name,
      moduleId,
      undefined, // body parameter
      fileParameter
    ).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.UPLOAD_SUCCESS');
      }),
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.UPLOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Upload multiple documents
   */
  uploadMultipleDocuments(requests: DocumentUploadRequest[]): Observable<any> {
    if (requests.length === 0) {
      return of([]);
    }

    // Group by category for batch upload
    const groupedRequests = this.groupRequestsByCategory(requests);
    const uploadPromises: Observable<any>[] = [];

    Object.keys(groupedRequests).forEach(categoryId => {
      const categoryRequests = groupedRequests[categoryId];
      const category = this.getCategoryById(categoryId);
      
      if (category) {
        const files = categoryRequests.map(req => req.file);
        const fileParameters: FileParameter[] = files.map(file => ({
          data: file,
          fileName: file.name
        }));

        const moduleId = this.getModuleIdForCategory(categoryId);
        const fileNames = files.map(file => file.name);

        const uploadObservable = this.minioService.uploadMultiple(
          moduleId,
          category.bucketName,
          fileNames,
          true, // continueOnError
          files.length, // maxFileCount
          undefined, // body
          fileParameters
        );

        uploadPromises.push(uploadObservable);
      }
    });

    // Execute all uploads
    return new Observable(observer => {
      Promise.all(uploadPromises.map(obs => obs.toPromise()))
        .then(results => {
          this.showSuccessMessage('DOCUMENTS.MULTIPLE_UPLOAD_SUCCESS');
          observer.next(results);
          observer.complete();
        })
        .catch(error => {
          this.showErrorMessage('DOCUMENTS.MULTIPLE_UPLOAD_FAILED');
          observer.error(error);
        });
    });
  }

  /**
   * Get documents list for a category
   */
  getDocuments(request: DocumentListRequest): Observable<DocumentMetadata[]> {
    // TODO: Implement actual API call to get documents list
    // For now, return mock data
    const mockDocuments: DocumentMetadata[] = [
      {
        id: 1,
        fileName: 'Fund Agreement.pdf',
        fileSize: 2048576,
        uploadDate: new Date(),
        category: request.category.id,
        bucketName: request.category.bucketName,
        fundId: request.fundId,
        title: 'Fund Agreement Document'
      },
      {
        id: 2,
        fileName: 'Investment Report Q1.pdf',
        fileSize: 1536000,
        uploadDate: new Date(),
        category: request.category.id,
        bucketName: request.category.bucketName,
        fundId: request.fundId,
        title: 'Q1 Investment Report'
      }
    ];

    return of(mockDocuments).pipe(
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.LOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Download a document
   */
  downloadDocument(documentId: number, bucketName: string): Observable<any> {
    return this.minioService.downloadFile(documentId, bucketName).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.DOWNLOAD_SUCCESS');
      }),
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.DOWNLOAD_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Preview a document
   */
  previewDocument(documentId: number, bucketName: string, expiryMinutes: number = 60): Observable<any> {
    const previewCommand = new MinIOPreviewCommand({
      id: documentId,
      bucketName: bucketName,
      expiryInMinutes: expiryMinutes
    });

    return this.minioService.preview(previewCommand).pipe(
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.PREVIEW_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Delete a document
   */
  deleteDocument(documentId: number, bucketName: string, deleteDatabaseRecord: boolean = true): Observable<any> {
    const deleteCommand = new MinIODeleteCommand({
      id: documentId,
      bucketName: bucketName,
      deleteDatabaseRecord: deleteDatabaseRecord
    });

    return this.minioService.deleteFile(deleteCommand).pipe(
      tap(() => {
        this.showSuccessMessage('DOCUMENTS.DELETE_SUCCESS');
      }),
      catchError(error => {
        this.showErrorMessage('DOCUMENTS.DELETE_FAILED');
        return throwError(error);
      })
    );
  }

  /**
   * Format file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (!bytes || bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // Private helper methods

  private getModuleIdForCategory(categoryId: string): number {
    // Map categories to module IDs
    const moduleMapping: { [key: string]: number } = {
      'fund-documents': 1,
      'reports': 2,
      'contracts': 3,
      'other': 4
    };

    return moduleMapping[categoryId] || 1;
  }

  private groupRequestsByCategory(requests: DocumentUploadRequest[]): { [categoryId: string]: DocumentUploadRequest[] } {
    return requests.reduce((groups, request) => {
      const categoryId = request.category.id;
      if (!groups[categoryId]) {
        groups[categoryId] = [];
      }
      groups[categoryId].push(request);
      return groups;
    }, {} as { [categoryId: string]: DocumentUploadRequest[] });
  }

  private showSuccessMessage(messageKey: string): void {
    const message = this.translate.instant(messageKey);
    this.toastr.success(message, this.translate.instant('COMMON.SUCCESS'));
  }

  private showErrorMessage(messageKey: string): void {
    const message = this.translate.instant(messageKey);
    this.toastr.error(message, this.translate.instant('COMMON.ERROR'));
  }
}
