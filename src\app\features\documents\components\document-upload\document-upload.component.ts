import { Component, Input, Output, EventEmitter, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule } from '@angular/forms';

// Shared components
import { FileUploadComponent } from '@shared/components/file-upload/file-upload.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';

// Services
import { DocumentManagementService, DocumentCategory, DocumentUploadRequest } from '../../services/document-management.service';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

export interface DocumentUploadDialogData {
  fundId?: number;
  selectedCategory?: DocumentCategory;
}

@Component({
  selector: 'app-document-upload',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatDialogModule,
    MatButtonModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    FormsModule,
    FileUploadComponent,
    CustomButtonComponent
  ],
  templateUrl: './document-upload.component.html',
  styleUrls: ['./document-upload.component.scss']
})
export class DocumentUploadComponent {
  @Output() uploadComplete = new EventEmitter<any>();

  // Form data
  selectedCategory = '';
  documentTitle = '';
  selectedFiles: File[] = [];
  isUploading = false;

  // Document categories
  documentCategories: DocumentCategory[] = [];

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private documentService: DocumentManagementService,
    private dialogRef: MatDialogRef<DocumentUploadComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DocumentUploadDialogData
  ) {
    this.documentCategories = this.documentService.getCategories();

    // Pre-select category if provided
    if (this.data?.selectedCategory) {
      this.selectedCategory = this.data.selectedCategory.id;
    }
  }

  onFileSelected(files: File | File[] | null): void {
    if (!files) {
      this.selectedFiles = [];
      return;
    }

    if (Array.isArray(files)) {
      this.selectedFiles = files;
    } else {
      this.selectedFiles = [files];
    }
  }

  onUpload(): void {
    if (!this.selectedCategory || this.selectedFiles.length === 0) {
      return;
    }

    const category = this.documentService.getCategoryById(this.selectedCategory);
    if (!category) {
      return;
    }

    this.isUploading = true;

    // Create upload requests for each file
    const uploadRequests: DocumentUploadRequest[] = this.selectedFiles.map(file => ({
      file: file,
      category: category,
      title: this.documentTitle,
      fundId: this.data?.fundId
    }));

    // Upload multiple documents
    this.documentService.uploadMultipleDocuments(uploadRequests).subscribe({
      next: (results) => {
        this.isUploading = false;
        this.uploadComplete.emit({
          category: this.selectedCategory,
          files: this.selectedFiles,
          title: this.documentTitle,
          results: results
        });
        this.dialogRef.close(true);
      },
      error: (error) => {
        console.error('Upload error:', error);
        this.isUploading = false;
      }
    });
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  isFormValid(): boolean {
    return !!(this.selectedCategory && this.selectedFiles.length > 0);
  }
}
